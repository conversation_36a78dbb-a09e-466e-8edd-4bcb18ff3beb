"use client"

import { useState, useContext, useEffect } from 'react';
import { Upload ,Info} from 'lucide-react';
import Logo from "../../../public/logo/kavia_logo.svg";
import Image from "next/image";
import ConfigureModal from "../Modal/ConfigureModel";
import { StateContext } from '../Context/StateContext';
import PropertiesRenderer from '../UiMetadata/PropertiesRenderer';
import { getArchitecturalRequirementWithChildren, createProjectGuidanceFlow,updateNodeByPriority } from '@/utils/api';
import { useRouter, usePathname, useSearchParams } from "next/navigation";
import { AlertContext } from '../NotificationAlertService/AlertList';
import { ProjectSetupContext } from '../Context/ProjectSetupContext';
import en from "@/en.json"
import StatusPanel from "@/components/StatusPanel/StatusPanel";
import { buildProjectUrl } from '@/utils/navigationHelpers';
import { ExecutionContext } from '../Context/ExecutionContext';

export default function ArchitecturalRequirementConfigurationStep({ setIsAutoConfiguration ,setIsArchitectureReqConfig}) {
  const [configMethod, setConfigMethod] = useState('discussion');
  const { projectId,showConfigModel, setShowConfigModel } = useContext(ProjectSetupContext);
  const [configureModel, setConfigureModel] = useState(false);
  const [loadingAutoConfigure, setLoadingAutoConfigure] = useState(false);
  const [architectureDetails, setArchitecturalDetails] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [archReqId, setArchReqId] = useState(null);
  const [isVisible, setIsVisible] = useState(true);

  const { showAlert } = useContext(AlertContext);
  const flag = sessionStorage.getItem(`openArchReqContent-${projectId}`);

  const pathname = usePathname();
  const searchParams = useSearchParams();
  const router = useRouter();
  const { setIsVertCollapse } = useContext(StateContext);

  const {configStatus,currentTaskId,setAutoNavigateEnabled} = useContext(ExecutionContext)
  const [taskStatus, setTaskStatus] = useState("Idle");

  // Track URL parameters
  useEffect(() => {
    const hasDiscussionParam = searchParams.has("discussion");
    const isCreatingArchReq = searchParams.has("is_creating_Arch_req");
    const nodeId = searchParams.get("node_id");
    const nodeType = searchParams.get("node_type");
    
    if (nodeId && nodeType === "ArchitecturalRequirement") {
      setArchReqId(nodeId);
    }
    
    // If the discussion parameter was previously present but now removed
    if (!hasDiscussionParam && !isCreatingArchReq && archReqId) {
      // Fetch updated architecture details to check configuration status
      getArchitecturalRequirementWithChildren(projectId)
        .then(data => {
          if (data && data.properties && data.properties.configuration_state === "configured") {
            // Store flag to indicate architecture content has been opened
            sessionStorage.setItem(`openArchReqContent-${projectId}`, "true");
            
            // Log the configuration to MongoDB
            createProjectGuidanceFlow(parseInt(projectId), {
              project_id: parseInt(projectId),
              step_name: "architecture_configuration",
              status: "completed",
              data: {
                architecture_id: parseInt(data.id),
                type: "ArchitecturalRequirement",
                status: "configured"
              }
            })
            .then(result => {
              
              // Set the architectural details
              setArchitecturalDetails(data);
            })
            .catch(error => {
              
            });
          }
        })
        .catch(error => {
          
        });
    }
  }, [searchParams, archReqId, projectId]);

  const handleCloseModal = () => {
    setConfigureModel(false);
  };

  const handleConfigureClick = () => {
    setConfigMethod('auto');
    setIsAutoConfiguration(true);
    setConfigureModel(true);
  };
const handlePropertyUpdate = async (key, value) => {
  try {
    const response = await updateNodeByPriority(architectureDetails.id, key, value);

    if (response === "success" || response?.status === "success") {
      // Update local state immediately
      setArchitecturalDetails((prev) => ({
        ...prev,
        properties: {
          ...prev.properties,
          [key]: value,
        },
      }));

      showAlert("Content updated successfully", "success");
    } else {
      showAlert("Failed to update content", "error");
    }
  } catch (error) {
   
    showAlert("Failed to update content", "error");
  }
};
  const updateRequirements = () => {
    setConfigMethod('discussion');
    setIsAutoConfiguration(false);
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("discussion", "new");
    newSearchParams.set("node_id", architectureDetails?.id);
    newSearchParams.set("node_type", "ArchitecturalRequirement");
    newSearchParams.set("is_creating_Arch_req", "true");
    const fullPath = `${buildProjectUrl(projectId, 'architecture/architecture-requirement')}?${newSearchParams.toString()}`;
  
    router.push(fullPath);
  };

  useEffect(()=>{
    if (configStatus[currentTaskId]) {
    
      setTaskStatus(configStatus[currentTaskId].task_status || "Idle");
      setAutoNavigateEnabled(false)
     
    }
  },[currentTaskId, configStatus[currentTaskId],projectId])

    useEffect(() => {
    const fetchArchRequirementsOnComplete = async () => {
      if (taskStatus.toLowerCase() === "complete" && projectId) {
        try {
          // Fetch the latest architectural requirements data when task is complete
          const archRequirements = await getArchitecturalRequirementWithChildren(projectId);
          setArchitecturalDetails(archRequirements);
         
          
         
          if (archRequirements && 
              archRequirements.properties && 
              archRequirements.properties.configuration_state === "configured") {     
                 setIsArchitectureReqConfig(true) 
           
            sessionStorage.setItem(`openArchReqContent-${projectId}`, "true");
            
          
            try {
              const result = await createProjectGuidanceFlow(parseInt(projectId), {
                project_id: parseInt(projectId),
                step_name: "architecture_configuration",
                status: "completed",
                data: {
                  architecture_id: parseInt(archRequirements.id),
                  type: "ArchitecturalRequirement",
                  status: "configured"
                }
              });
              
            } catch (error) {
              
            }
          }
        } catch (error) {
          
        } finally {
          setIsLoading(false);
        }
      }
    };

    fetchArchRequirementsOnComplete();
  }, [taskStatus, projectId, flag]);

  const LoaderComponent = ()=>{
    
      return (
        <div className="flex gap-4 p-4">
       
    
          <div className="p-4 space-y-6 flex-1">
            <div className="border border-gray-200 flex p-4 flex-col space-y-3">
              <div className="h-6 bg-gray-100 animate-pulse rounded-lg w-2/3"></div>
              <div className="flex items-center justify-between">
                <div className="h-5 bg-gray-100 animate-pulse rounded-lg w-1/3"></div>
                <div className="h-8 bg-gray-100 animate-pulse rounded-lg w-1/4"></div>
              </div>
            </div>
    
            <div className="border border-gray-200 rounded-lg p-4 space-y-3">
              <div className="h-5 bg-gray-100 animate-pulse rounded-lg w-1/4"></div>
              <div className="h-3 bg-gray-100 animate-pulse rounded-lg w-full"></div>
              <div className="h-3 bg-gray-100 animate-pulse rounded-lg w-5/6"></div>
              <div className="h-3 bg-gray-100 animate-pulse rounded-lg w-4/6"></div>
            </div>
    
            <div className="border border-gray-200 rounded-lg p-4 space-y-3">
              <div className="h-5 bg-gray-100 animate-pulse rounded-lg w-1/4"></div>
              <div className="h-3 bg-gray-100 animate-pulse rounded-lg w-full"></div>
              <div className="h-3 bg-gray-100 animate-pulse rounded-lg w-5/6"></div>
              <div className="h-3 bg-gray-100 animate-pulse rounded-lg w-4/6"></div>
            </div>
    
             
          </div>
        </div>
      );
  
  }

  const InformationBadge = ()=>{
    return (
      <div className="w-full mx-auto my-4">
        <div className="bg-gradient-to-r from-primary-100 to-indigo-100 border-l-4 border-primary p-5 rounded-lg shadow-lg flex items-start gap-4">
          <div className="flex-shrink-0 bg-primary-100 p-2 rounded-full">
            <Info className="h-5 w-5 text-primary" />
          </div>
          <div className="flex-1">
            <h4 className="font-weight-medium text-primary-800 mb-1">Optional Configuration</h4>
            <p className="typography-body-sm text-primary-700">
              Setting up architectural requirements is optional at this stage. You can click <span className="font-weight-medium">Continue</span> to move on to System Context.
              <br />
              If needed, you can always add or update architectural requirements later under the <span className="font-weight-medium">Architecture</span> section after creating the project.
            </p>
          </div>
          <button 
            onClick={() => setIsVisible(false)}
            className="mt-1 text-primary-400 hover:text-primary transition-colors focus:outline-none"
            aria-label="Dismiss"
          >
            <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>
    );
    
  }

  // Fetch architecture requirements only in useEffect
  useEffect(() => {
    const getArchRequirements = async () => {
      setIsLoading(true);
      try {
        const archRequirements = await getArchitecturalRequirementWithChildren(projectId);
        setArchitecturalDetails(archRequirements);
        
        // If architectural requirements are already configured, store it in MongoDB
        if (archRequirements && 
            archRequirements.properties && 
            archRequirements.properties.configuration_state === "configured" && 
            !flag) {
          // Store flag to indicate architecture content has been configured
          sessionStorage.setItem(`openArchReqContent-${projectId}`, "true");
          
          // Log the configuration to MongoDB
          try {
            const result = await createProjectGuidanceFlow(parseInt(projectId), {
              project_id: parseInt(projectId),
              step_name: "architecture_configuration",
              status: "completed",
              data: {
                architecture_id: parseInt(archRequirements.id),
                type: "ArchitecturalRequirement",
                status: "configured"
              }
            });
            
          } catch (error) {
            
          }
        }
      } catch (error) {
        
      } finally {
        setIsLoading(false);
      }
    };

    if (projectId) {
      getArchRequirements();
    }
    
    // Use searchParams.toString() instead of the object reference
  }, [projectId, searchParams.toString(), flag]);

  if (isLoading) {
    return <LoaderComponent />;
  }

   const shouldShowStatusPanel = () => {
    if (!showConfigModel) return false;
    const hasOngoingTask = currentTaskId && configStatus[currentTaskId];
    
    // Only show status panel for auto configuration tasks
    if (hasOngoingTask) {
      const taskInProgress = taskStatus.toLowerCase() === 'in_progress' || taskStatus.toLowerCase() === 'idle';
      const isConfigured = architectureDetails && 
                          architectureDetails.properties && 
                          architectureDetails.properties.configuration_state === "configured";
      const taskNotComplete = taskStatus.toLowerCase() !== 'complete';
      
      return taskInProgress || (isConfigured && taskNotComplete);
    }
    
    // For interactive configuration, never show status panel
    return false;
  };

  return (
    <div className="h-full  overflow-y-auto px-4">
      {shouldShowStatusPanel()?(
       <StatusPanel />
      ) : (
        <>
      {configureModel && (
        <ConfigureModal
          id={projectId}
          type={"Architecture"}
          isNodeType={"Architecture"}
          closeModal={handleCloseModal}
          isCreateProject={true}
          setShowConfigModel={setShowConfigModel}
          setLoadingAutoConfigure={setLoadingAutoConfigure}
          onSubmitSuccess={() => {
            showAlert(
              `Architecture configuration initiated successfully`,
              "success"
            );
            setIsVertCollapse(false);
            
            // Log successful configuration to MongoDB
            createProjectGuidanceFlow(parseInt(projectId), {
              project_id: parseInt(projectId),
              step_name: "architecture_configuration",
              status: "completed",
              data: {
                architecture_id: architectureDetails ? parseInt(architectureDetails.id) : null,
                type: "ArchitecturalRequirement",
                status: "configured"
              }
            })
            .then(result => {
              
              // Set the flag to indicate architecture has been configured
              sessionStorage.setItem(`openArchReqContent-${projectId}`, "true");
            })
            .catch(error => {
              
            });
          }}
        />
      )}

      {flag && architectureDetails && (!currentTaskId || !configStatus[currentTaskId] || taskStatus.toLowerCase() === 'complete')? (
        <div>
          {architectureDetails?.properties?.Title && (
            <div className="typography-body-lg font-weight-medium text-gray-800">
              Title: {architectureDetails.properties.Title}
            </div>
          )}

          <PropertiesRenderer
            properties={architectureDetails.properties}
            metadata={architectureDetails.ui_metadata}
            to_skip={["configuration_state"]}
            to_show={[
              "Description",
              "functional_requirements",
              "architectural_requirements",
              "Details",
            ]}
            onUpdate={handlePropertyUpdate}
          />
        </div>
      ) : (
        <>
        {/* {isVisible && (
           <InformationBadge />
        )} */}
          <div className="flex-1 p-4 flex flex-col">
            <div className="mb-6">
              <h3 className="typography-body-lg font-weight-medium mb-4">Configuration Method</h3>

              <div className="grid grid-cols-1 md:grid-cols-6 gap-6">
                {/* Update via discussion card */}
                <div
                  className={`col-span-1 md:col-span-1 lg:col-span-2 border rounded-lg p-6 cursor-pointer transition-all hover:shadow-md ${
                    configMethod === 'discussion' ? 'border-primary bg-primary-50' : 'border-gray-200'
                  }`}
                  onClick={updateRequirements}
                >
                  <div className="flex mb-4 items-center space-x-2">
                    <div className="py-1 px-1.5 bg-primary-100 rounded-lg flex items-center justify-center">
                      <Image
                        src={Logo}
                        alt="Logo"
                        width={16}
                        height={16}
                        className="text-primary"
                      />
                    </div>
                    <h4 className="typography-body-lg font-weight-medium">Interactive configuration</h4>
                  </div>

                  <p className="text-gray-600">
                   {en.ArchReqUpdate}
                  </p>
                </div>

                {/* Auto Configuration card */}
                <div
                  className={`col-span-1 md:col-span-1 lg:col-span-2 border rounded-lg p-6 cursor-pointer transition-all hover:shadow-md  ${
                    configMethod === 'auto' ? 'border-primary bg-primary-50' : 'border-gray-200'
                  }`}
                  onClick={handleConfigureClick}
                >
                  <div className="flex mb-4 items-center space-x-2">
                    <div className="p-1.5 bg-primary-100 rounded-lg flex items-center justify-center">
                      <Upload className="w-4 h-4  text-primary text-bold" />
                    </div>
                    <h4 className="typography-body-lg font-weight-medium">Auto Configuration</h4>
                  </div>

                  <p className="text-gray-600">
                    {en.ArchReqAutoConfig}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </>
      )}
      </>)}
    </div>
  );
}