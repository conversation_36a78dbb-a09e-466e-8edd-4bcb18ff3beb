import { PLATFORMS } from '../../constants/code_gen/platforms';

// Map PLATFORMS to the format expected by the component
const buildOptions = PLATFORMS.map(platform => ({
  id: platform.key === 'web' ? 'web' :
      platform.key === 'mobile' ? 'mobile' :
      platform.key === 'backend' ? 'backend' :
      platform.key === 'fullstack' ? 'fullstack' :
      platform.key === 'generic' ? 'generic' : platform.key,
  label: platform.label,
  isDefault: platform.key === 'web',
  icon: platform.icon
}));

const BuildOptionsButtons = ({ disabled, buildOption, setBuildOption, isLight }) => {
  // Convert to single selection - use first element if array, otherwise use as number
  const selectedOption = Array.isArray(buildOption) ? buildOption[0] || 0 : buildOption || 0;
  
  const handleOptionClick = (index) => {
    if (disabled) return;
    
    // Single selection - just set the clicked option
    setBuildOption(index);
  };

  const isSelected = (index) => selectedOption === index;

  return (
    <div className="w-full mt-8 flex gap-2 justify-center items-center">
      {buildOptions.map((option, index) => (
        <div
          key={option.id}
          className={`${disabled ? 'opacity-75 cursor-not-allowed' : 'cursor-pointer'}
          flex-1 relative rounded-lg transition-all duration-200 group
          ${isSelected(index)
              ? `border border-[hsl(var(--primary))] bg-gradient-to-r from-[hsl(var(--primary))]/15 via-[hsl(var(--primary))]/8 to-[hsl(var(--primary))]/5 backdrop-blur-[17px] scale-[1.02]`
              : `border ${isLight
                  ? "hover:border-primary-600 border-gray-200 hover:bg-gradient-to-r hover:from-amber-100/60 hover:from-10% hover:via-amber-100/40 hover:via-30% hover:to-amber-100/40 hover:to-90%"
                  : "hover:border-primary-700 border-transparent hover:bg-gradient-to-r hover:from-amber-900/40 hover:from-10% hover:via-amber-700/10 hover:via-30% hover:to-amber-700/10 hover:to-90%"
                }`
            }`}
          onClick={() => handleOptionClick(index)}
          title={option.id !== 'web' ? `${option.label} - Experimental feature` : option.label}
        >
          {/* Theme-matching hover tooltip for experimental options */}
          {option.id !== 'web' && (
            <div className={`absolute -top-10 left-1/2 transform -translate-x-1/2 px-3 py-1.5 text-xs rounded-md opacity-0 group-hover:opacity-100 transition-all duration-200 pointer-events-none z-20 whitespace-nowrap backdrop-blur-sm border ${
              isLight
                ? "bg-white/90 text-gray-700 border-gray-300/60 shadow-md"
                : "bg-[hsl(var(--background))]/95 text-[hsl(var(--foreground))] border-[hsl(var(--border))]/60 shadow-lg"
            }`}>
              <div className="font-medium text-[hsl(var(--primary))]">Experimental</div>
              {/* Tooltip arrow */}
              <div className={`absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-3 border-r-3 border-t-3 border-transparent ${
                isLight ? "border-t-white/90" : "border-t-[hsl(var(--background))]/95"
              }`}></div>
            </div>
          )}

          <div className={`${isSelected(index)
              ? `${isLight ? "bg-white/70" : "bg-white/10"} backdrop-blur-sm rounded-lg py-1.5 px-2 w-full flex flex-row justify-center items-center transition duration-300`
              : `${isLight ? "bg-white/70" : "bg-white/10"} backdrop-blur-sm rounded-lg py-1.5 px-2 w-full flex flex-row justify-center items-center transition duration-300`
            }`}>
            <span className="mr-1">
              {option.icon}
            </span>
            <p className={`${isSelected(index)
                ? (isLight ? "text-black typography-body-sm font-weight-normal leading-[21px]" : "text-white typography-body-sm font-weight-normal leading-[21px]")
                : `text-md whitespace-nowrap ${isSelected(index)
                    ? (isLight ? "text-gray-800" : "text-gray-200")
                    : (isLight ? "text-gray-600" : "text-gray-400")
                  }`
              }`}>
              {option.label}
            </p>
            {/* Single selection indicator */}
            {isSelected(index) && (
              <div className="ml-1 w-2 h-2 bg-[hsl(var(--primary))] rounded-full flex-shrink-0"></div>
            )}
          </div>
        </div>
      ))}
    </div>
  );
};

export { buildOptions };
export default BuildOptionsButtons;