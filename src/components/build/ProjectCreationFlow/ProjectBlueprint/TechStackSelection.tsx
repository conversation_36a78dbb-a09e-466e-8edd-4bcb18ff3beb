import React from 'react';
import { Server, Laptop, Database } from 'lucide-react';

/**
 * TO SUPPORT FRONTEND, BACKEND, AND DATABASE SIMULTANEOUSLY:
 * 
 * 1. Remove or modify the warning message at the bottom to support all
 *    selections being active at once. For example, only show the warning
 *    when ALL are set to "None".
 * 
 * 2. Adjust the visual styling (border colors, background colors) to properly
 *    reflect that all selections can be made at the same time.
 * 
 * 3. Consider adding an informational UI element to show the user that all
 *    selections are allowed and explain how they will work together.
 */

interface TechStackSelectionProps {
  techStack?: {
    frontend?: string[];
    backend?: string[];
    database?: string[];
  };
  frontendOptions: string[];
  backendOptions: string[];
  databaseOptions: string[];
  onChange: (category: 'frontend' | 'backend' | 'database', value: string) => void;
  isDarkMode?: boolean;
}

const TechStackSelection: React.FC<TechStackSelectionProps> = ({
  techStack = { frontend: ["None"], backend: ["None"], database: ["None"] },
  frontendOptions,
  backendOptions,
  databaseOptions,
  onChange,
  isDarkMode = false
}) => {
  // Extract values safely with defaults if undefined
  const frontendValue = techStack?.frontend?.[0] || "None";
  const backendValue = techStack?.backend?.[0] || "None";
  const databaseValue = techStack?.database?.[0] || "None";

  return (
    <div className="mb-6">
      <h3 className="typography-body-lg font-weight-medium mb-4 text-gray-800">Tech Stack</h3>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Frontend Selection */}
        <div 
          className={`border rounded-lg p-4 transition-colors duration-200 ${
            frontendValue === "None" 
              ? "border-gray-200 bg-gray-50"
              : "border-[hsl(var(--primary))]/20 bg-primary-50"
          }`}
        >
          <div className="flex items-center mb-3">
            <Laptop 
              size={18} 
              className={`mr-2 transition-colors duration-200 ${
                frontendValue === "None" 
                  ? "text-gray-400"
                  : "text-[hsl(var(--primary))]"
              }`} 
            />
            <h4 
              className={`font-weight-medium transition-colors duration-200 ${
                frontendValue === "None" 
                  ? "text-gray-500"
                  : "text-gray-800"
              }`}
            >
              Frontend
            </h4>
          </div>
          <select
            value={frontendValue}
            onChange={(e) => onChange('frontend', e.target.value)}
            className={`w-full p-2 border rounded-md focus:ring-1 focus:ring-[hsl(var(--primary))] transition-colors duration-200 ${
              frontendValue === "None" 
                ? "border-gray-300 bg-gray-100 text-gray-500"
                : "border-[hsl(var(--primary))]/20 bg-white text-gray-800"
            }`}
          >
            {frontendOptions.map(option => (
              <option key={option} value={option}>{option}</option>
            ))}
          </select>
        </div>
        
        {/* Backend Selection */}
        <div 
          className={`border rounded-lg p-4 transition-colors duration-200 ${
            backendValue === "None" 
              ? "border-gray-200 bg-gray-50"
              : "border-[hsl(var(--primary))]/20 bg-primary-50"
          }`}
        >
          <div className="flex items-center mb-3">
            <Server 
              size={18} 
              className={`mr-2 transition-colors duration-200 ${
                backendValue === "None" 
                  ? "text-gray-400"
                  : "text-[hsl(var(--primary))]"
              }`} 
            />
            <h4 
              className={`font-weight-medium transition-colors duration-200 ${
                backendValue === "None" 
                  ? "text-gray-500"
                  : "text-gray-800"
              }`}
            >
              Backend
            </h4>
          </div>
          <select
            value={backendValue}
            onChange={(e) => onChange('backend', e.target.value)}
            className={`w-full p-2 border rounded-md focus:ring-1 focus:ring-[hsl(var(--primary))] transition-colors duration-200 ${
              backendValue === "None" 
                ? "border-gray-300 bg-gray-100 text-gray-500"
                : "border-[hsl(var(--primary))]/20 bg-white text-gray-800"
            }`}
          >
            {backendOptions.map(option => (
              <option key={option} value={option}>{option}</option>
            ))}
          </select>
        </div>

        {/* Database Selection */}
        <div 
          className={`border rounded-lg p-4 transition-colors duration-200 ${
            databaseValue === "None" 
              ? "border-gray-200 bg-gray-50"
              : "border-[hsl(var(--primary))]/20 bg-primary-50"
          }`}
        >
          <div className="flex items-center mb-3">
            <Database 
              size={18} 
              className={`mr-2 transition-colors duration-200 ${
                databaseValue === "None" 
                  ? "text-gray-400"
                  : "text-[hsl(var(--primary))]"
              }`} 
            />
            <h4 
              className={`font-weight-medium transition-colors duration-200 ${
                databaseValue === "None" 
                  ? "text-gray-500"
                  : "text-gray-800"
              }`}
            >
              Database
            </h4>
          </div>
          <select
            value={databaseValue}
            onChange={(e) => onChange('database', e.target.value)}
            className={`w-full p-2 border rounded-md focus:ring-1 focus:ring-[hsl(var(--primary))] transition-colors duration-200 ${
              databaseValue === "None" 
                ? "border-gray-300 bg-gray-100 text-gray-500"
                : "border-[hsl(var(--primary))]/20 bg-white text-gray-800"
            }`}
          >
            {databaseOptions.map(option => (
              <option key={option} value={option}>{option}</option>
            ))}
          </select>
        </div>
      </div>
      
      {/* Warning if all three are None - require at least one selection */}
      {frontendValue === "None" && backendValue === "None" && databaseValue === "None" && (
        <div className="mt-3 px-4 py-2 rounded-md typography-body-sm bg-red-50 border border-red-200 text-red-700">
          Please select at least one option from Frontend, Backend, or Database.
        </div>
      )}
      
      {/* Warning if database is selected but backend is None */}
      {databaseValue !== "None" && backendValue === "None" && (
        <div className="mt-3 px-4 py-2 rounded-md typography-body-sm bg-yellow-50 border border-yellow-200 text-yellow-700">
          You need to choose a backend if you select a database.
        </div>
      )}
    </div>
  );
};

export default TechStackSelection; 