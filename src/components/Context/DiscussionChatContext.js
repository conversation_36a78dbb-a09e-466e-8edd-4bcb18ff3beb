"use client"
import React, { createContext, useState, useEffect, useRef, useCallback, useContext } from "react";
import { useParams, useSearchParams, useRouter, usePathname } from "next/navigation";
import { startDiscussion, getDiscussion, repeatStep } from "@/utils/api";
import { fetchEventSource } from "@microsoft/fetch-event-source";
import { getCookie } from "../../utils/auth";
import { v4 as uuid } from "uuid";
import { usePlanRestriction } from "./PlanRestrictionContext";
import { registerCodeQueryAgent, getCodeQueryLLMModels } from "@/utils/api";
import Cookies from 'js-cookie';
import { AlertContext } from "../NotificationAlertService/AlertList";

const DiscussionChatContext = createContext();

const DiscussionChatProvider = ({ children }) => {
  const discussionStateRef = useRef("idle");
  const params = useParams();
  const searchParams = useSearchParams();
  const router = useRouter();
  const [isDiscussionModalOpen, setIsDiscussionModalOpen] = useState(false);
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [steps, setSteps] = useState([]);
  const [executeStatus, setExecuteStatus] = useState(false);
  const [discussionId, setDiscussionId] = useState(null);
  const discussionIdRef = useRef(discussionId);
  const [modifications, setModifications] = useState([]);
  const [nodeId, setNodeId] = useState(null);
  const [nodeType, setNodeType] = useState(null);
  const [initialized, setInitialized] = useState(false);
  const [loading, setLoading] = useState(false);
  const [processing, setProcessing] = useState(false);
  const [currentMessage, setCurrentMessage] = useState("");
  const currentMessageRef = useRef("")
  const { setShowPlanRestriction, setCreditLimitCrossed } = usePlanRestriction();
  const [allUsers,setAllUsers] = useState([]);
  const discussionClosed = useRef(false);
  const pathName = usePathname();
  const projectId = pathName.split("/")[3];
  const [availableModels, setAvailableModels] = useState([]);
  const [selectedModel, setSelectedModel] = useState('');
  const [isLoadingModels, setIsLoadingModels] = useState(false);
  const [openProjectContent,setOpenProjectContent] = useState(false)
  const executionMessages = {
    initialize: "Setting up your discussion...",
    retrieve_info: "Gathering relevant information...",
    main_discussion: "Processing your message...",
    merge_captured_items: "Applying changes...",
    finalize: "Wrapping up the discussion..."
  };
  const [welcomeMessageShown, setWelcomeMessageShown] = useState(false);
  const errorMessages = {
    initialize: "Oops! We couldn't start the discussion. Please try again.",
    retrieve_info: "We had trouble retrieving information. Let's continue and try again later.",
    main_discussion: "Sorry, I couldn't process that. Something went wrong",
    merge_captured_items: "Changes couldn't be applied. Please review and try again.",
    finalize: "Unable to save the discussion. Don't worry, your changes are still here."
  };
  const abortControllerRef = useRef(new AbortController());
  const retryCounter = useRef(0);
  const [messages, setMessages] = useState([
    { id: 1, text: executionMessages["initialize"], sender: 'AI', messageUuid: uuid() }
  ]);

  const { showAlert } = useContext(AlertContext);

  // New WebSocket states
  const [wsConnection, setWsConnection] = useState(null);
  const [wsStatus, setWsStatus] = useState("disconnected");
  const [reconnectAttempt, setReconnectAttempt] = useState(0);
  const maxReconnectAttempts = 5;
  const [allDocuments, setAllDocuments] = useState([]);

  useEffect(() => {
    discussionIdRef.current = discussionId
  }, [discussionId]);

  const connectWebSocket = useCallback((sessionId) => {
    if (!sessionId) return;

    const queryDiscussionId = searchParams.get('query_discussion_id') || discussionId;

    if(wsConnection){
      wsConnection.close();
    }

    setWsStatus("connecting");
    const ws = new WebSocket(`${process.env.NEXT_PUBLIC_WS_URL}/${sessionId}`);

    ws.onopen = () => {
      setWsStatus("connected");
      setReconnectAttempt(0);
      ws.send(JSON.stringify({
        type: "client",
        task_id: sessionId,
      }));
    };

    ws.onmessage = async (event) => {
      const data = JSON.parse(event.data);
      const timestamp = new Date().toISOString();

      if(data.status && data.status == "agent_not_found"){
        let session_id = data.session_id

        if(session_id){
          const registerResponse = await registerCodeQueryAgent(session_id);
          if(registerResponse.status == 'success' && currentMessageRef.current){
            ws.send(JSON.stringify({
              type: "user_input",
              task_id: session_id,
              session_id: session_id,
              project_id: projectId,
              user_id: Cookies.get('userId'),
              discussion_id: searchParams.get('query_discussion_id'),
              auth_token: Cookies.get('idToken'),
              tenant_id: Cookies.get('tenant_id'),
              input: currentMessageRef.current
            }));
            currentMessageRef.current = ""
          }
          else {
            addMessage({
              id: Date.now(),
              text: `Connection error: Couldn't connect to the discussion agent. Please restart the discussion.`,
              sender: "AI",
              message_end: true
            });
          }
        }
      }

      if(data.data){
        if (data.data.status && data.data.status == 402){
          setShowPlanRestriction(true);
          setCreditLimitCrossed(true);
        }

        {
          if (data.data.discussion_id && !queryDiscussionId && !discussionClosed.current) {
            setDiscussionId(data.discussion_id);

            // Update URL with new discussion ID
            const url = new URL(window.location.href);
            url.searchParams.set('query_discussion_id', data.data.discussion_id);
            router.replace(url.pathname + url.search, undefined, { shallow: true });
          }
        }
      }

      sessionStorage.setItem(`message_status_${sessionId}`, 'received');


      switch (data.type) {

        case "disconnected":
          setWsStatus("disconnected");
          setProcessing(false);
          break;

        case "code_query":
          updateWebSocketMessage(data, timestamp);
          break;

        case "input_received":
          setProcessing(false);
          // setLoading(false);
          break;

        default:

      }
    };

    ws.onerror = (error) => {

      setWsStatus("error");
    };

    ws.onclose = () => {
      setWsStatus("disconnected");

    };

    setWsConnection(ws);
    return () => {
      if (ws) {
        ws.close();
        setWsConnection(null);
      }
    };
  }, [reconnectAttempt, maxReconnectAttempts]);

  // Effect to manage WebSocket connection based on query params
  useEffect(() => {
    const querySessionId = searchParams.get("sessionId");

    const fetchLLMModels = async () => {
      try {
        setIsLoadingModels(true);
        const models = await getCodeQueryLLMModels();

        if (models) {
          setAvailableModels(models);
        }
      }
      catch (error) {

      }
      finally {
        setIsLoadingModels(false);
      }
    }
    if (querySessionId && wsStatus == "disconnected") {
      connectWebSocket(querySessionId);
      if (availableModels.length == 0) {
        fetchLLMModels();
      }
    }
  }, [searchParams]);

  // Persist selectedModel per sessionId
  useEffect(() => {
    const sessionId = searchParams.get('sessionId');
    if (sessionId) {
      // On mount, restore selectedModel from sessionStorage if available
      const storedModel = sessionStorage.getItem(`selectedModel_${sessionId}`);
      if (storedModel) {
        setSelectedModel(storedModel);
      }
    }
  }, [searchParams]);

  // When selectedModel changes, persist it for the current sessionId
  useEffect(() => {
    const sessionId = searchParams.get('sessionId');
    if (sessionId && selectedModel) {
      sessionStorage.setItem(`selectedModel_${sessionId}`, selectedModel);
    }
  }, [selectedModel, searchParams]);

  // Cleanup effect
  useEffect(() => {
    return () => {
      if (wsConnection) {
        wsConnection.close();
        setWsConnection(null);
      }
    };
  }, []);

  // Use effect to check if discussion is existing or new
  useEffect(() => {

    // Only show welcome message if it hasn't been shown before and sessionId exists
    if (searchParams.get('sessionId') && !welcomeMessageShown) {
      removeLastMessage();
      addMessage({
        text: `Welcome to the Code Query Discussion Panel! Dive into your selected codebase and ask any questions`,
        sender: 'AI'
      });
      setWelcomeMessageShown(true); // Mark welcome message as shown
      return; // Exit early to prevent other API calls
    } else {
      let condition = searchParams.get('discussion') == 'existing' && searchParams.get('discussion_id') && !searchParams.get('stepName') && discussionStateRef.current == 'idle' ? true : false;
      if (condition && discussionIdRef.current) {
        checkForDiscussion()  //only fetch history if discussion id exists
      }
      if (searchParams.get('discussion') == 'new' && isDiscussionModalOpen && !initialized && searchParams.get('node_id') && searchParams.get('node_type') && discussionStateRef.current == 'idle') {
        startNewDiscussion(searchParams.get('node_id'), searchParams.get('node_type'));
      }
    }

  }, [discussionId, searchParams.get('discussion'), isDiscussionModalOpen, searchParams.get('sessionId'), welcomeMessageShown]);

  function extractLLMText(discussionString) {
    try {
      const discussion = JSON.parse(discussionString);

      // Find the assistant's message (LLM response)
      const assistantMessage = discussion.find(msg => msg.role === 'assistant');

      if (assistantMessage && assistantMessage.content) {
        // Clean up the escaped characters and format the text
        let cleanText = assistantMessage.content
          .replace(/\\n/g, '\n')
          .replace(/\\"/g, '"')
          .replace(/\\u00d7/g, '×')
          .replace(/\\\\/g, '\\');

        return cleanText;
      }

      return null;
    } catch (error) {
      return null;
    }
  }

  const checkForDiscussion = async () => {

    if (searchParams.get('discussion') == 'existing' && searchParams.get('discussion_id')) {
      let discussion = await getDiscussion(searchParams.get('discussion_id'));

      let discussionMessages = discussion.messages;
      // check if exists or add empty array
      let modifications = discussion.properties.modifications_history || null;
      let discussion_so_far = discussion.properties.discussion_so_far
      if (modifications) {
        modifications = JSON.parse(modifications);
        setModifications(modifications);
      }
      if (discussion_so_far) {
        const discussion_data = extractLLMText(discussion_so_far)
        addMessage({ text: discussion_data, sender: 'AI' })
      }
      if (discussionMessages && discussionMessages.length > 0) {
        discussionMessages = discussionMessages.map((message, index) => {
          let messageObj = { id: index, text: message.content };
          if (message.created_at) {
            messageObj.timestamp = message.created_at;
          }
          if (message.file_attachments) {
            messageObj.file_attachments = message.file_attachments
          }
          switch (message.role) {
            case 'user':
              messageObj.sender = 'User';
              if (message.user_id) {
                messageObj.userId = message.user_id;
                if (message.user_details) {
                  messageObj.userDetails = message.user_details
                }
              }
              break;
            case 'assistant':
              messageObj.sender = 'AI';
              break;
            default:
              messageObj.sender = 'User';
          }
          let data = messageObj.text
          const cleanText = data?.replace(/<JSON>[\s\S]*?<\/JSON>/, '').trim()
          messageObj.text = cleanText
          //  messageObj.text = renderHTML(cleanText)
          return messageObj;
        });

        if (discussion_so_far) {
          const discussion_data = extractLLMText(discussion_so_far);
          discussionMessages.unshift({
            id: -1,
            text: discussion_data,
            sender: 'AI',
          });
        }
        if (discussionId) {
          setMessages(discussionMessages);
        }
      }
      setSteps(discussion.steps);
      setNodeId(discussion.node_id);
      if (searchParams.get('node_type') === "Design") {
        setNodeType(searchParams.get('node_type'));
      }else {
        setNodeType(discussion.node_type);
      }

    }
  }

  // used for instantly setting status of discussion to closed to stop some live process from occuring immediately
  const setInstantCloseStatus = () => {
    discussionClosed.current = true;
  }

  const startNewDiscussion = async (nodeId, nodeType) => {
    setNodeId(searchParams.get('node_id'));
    setNodeType(searchParams.get('node_type'));
    let discussionType = searchParams.get('discussion_type') || searchParams.get('discussionType') || null;
    let response
    if (!initialized && nodeId && nodeType) {
      try {
        if (discussionType) {
          response = await startDiscussion(nodeId, nodeType, discussionType);
        } else {
          response = await startDiscussion(nodeId, nodeType);
        }

        if (!discussionClosed.current) {
          setDiscussionId(response.discussion_id);
          const url = new URL(window.location.href);
          url.searchParams.set('discussion', 'existing');
          url.searchParams.set('discussion_id', response.discussion_id);
          router.replace(url.pathname + url.search, undefined, { shallow: true });
          setSteps(response.steps);
          removeLastMessage();
          addMessage({
            text: `Welcome to the ${nodeType} ${response.discussion_type} discussion. During this session, we will ask you specific questions to gather essential details about the ${nodeType}. These details will be used to set up the project information in our database.  
          Once you are confident that all the required information has been gathered and reviewed, you can press the **Merge** button. The **Merge** button will combine all the collected information and finalize the project setup by saving it to the database. Please make sure everything is accurate before merging, as this step cannot be undone.`,
            sender: 'AI'
          });

          setLoading(false);
          setInitialized(true);
          setTimeout(() => {
            handleStepExecution(response.steps[1], 1, null, "execute", { ...response, discussion_id: response.discussion_id, steps: response.steps, node_id: nodeId, node_type: nodeType });
          }, 10);
        } else {
          discussionClosed.current = false;
        }
      }
      catch (error) {

      }
    }
  };

  const updateWebSocketMessage = (data, timestamp) => {
    if (data.data && data.data.message) {
      const messageUuid = data.data.message_uuid;
      const message_end = data.data.message_end;
      const message_status = data.data.status || "";

      if (message_end) {
        setProcessing(false);
      }
      else if (message_status == "streaming") {
        setProcessing(true);
      }

      setMessages((prevMessages) => {
        const existingMessageIndex = prevMessages.findIndex(
          (msg) => msg.messageUuid === messageUuid
        );
        if (existingMessageIndex !== -1) {
          // Update existing message
          const updatedMessages = [...prevMessages];
          updatedMessages[existingMessageIndex] = {
            ...updatedMessages[existingMessageIndex],
            text: data.data.message,
            message_end: message_end || false,
            timestamp
          };
          return updatedMessages;
        } else {
          // Add new message
          return [...prevMessages, {
            id: Date.now(),
            text: data.data.message,
            sender: 'AI',
            message_end: message_end || false,
            timestamp,
            messageUuid
          }];
        }
      });


    }
  };

  const handleStepExecution = async (step, index, usercomment, executionType = "repeat", discussionParams = {}, merge_index = null, file_attachments = {}) => {
    if (!searchParams.get('discussion')) {
      return;
    } else {
      // Original SSE path for main discussion
      if (step.name == "main_discussion" && !usercomment) {
        usercomment = currentMessage;
      }
      let updatedSteps = [...steps];
      if (updatedSteps.length == 0) {
        updatedSteps = discussionParams.steps;
      }
      updatedSteps[index].status = "Processing";
      setSteps(updatedSteps);
      addMessage({ text: executionMessages[step.name] || `Processing...`, sender: 'AI' });
      setProcessing(true);
      if (searchParams.get('discussion')) {
        abortControllerRef.current = new AbortController(); //assign new abort controller every time else the old controller will be there
        retryCounter.current = 0;
        if (step.response.is_streaming) {
          // For streaming responses, we'll continue to use fetchEventSource
          // The URL structure needs to be updated to match the new POST endpoint
          let url = `${process.env.NEXT_PUBLIC_API_URL}/discussion/${executionType}/`;
          const eventSource = fetchEventSource(url, {
            method: 'POST',
            headers: {
              "Content-Type": "application/json",
              "Authorization": `Bearer ${await getCookie('idToken')}`,
              "X-Tenant-Id": `${await getCookie('tenant_id')}`
            },
            body: JSON.stringify({
              node_type: discussionParams.node_type || nodeType,
              node_id: nodeId || discussionParams.node_id,
              step_name: step.name,
              discussion_id: discussionParams.discussion_id || discussionId,
              usercomment: usercomment,
              modification_index: merge_index,
              file_attachments: file_attachments
            }),
            signal: abortControllerRef.current.signal,
            openWhenHidden: true,
            onopen: (response) => {
              return Promise.resolve();
            },
            onmessage: (event) => {
              try {
                if (loading) {
                  setLoading(false); //processing is set to true earlier so we can set loading to false
                }
                if (discussionIdRef.current) {
                  let data = JSON.parse(event.data);
                  if (data.modifications) {
                    setModifications((data.modifications))
                    return
                  }
                  let content = data.content;
                  if (data.status) {
                    content = content + `<br> <div className="status-container flex items-center space-x-2">
                      <div className="spinner animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-primary"></div>
                      <span className="text-gray-600">${data.status}</span>
                    </div>`;
                  }
                  removeLastMessage();
                  if (!discussionClosed.current && discussionIdRef.current) {
                    addMessage({ text: content, sender: 'AI' });
                  }
                }
                else {
                  if (abortControllerRef.current) {
                    abortControllerRef.current.abort();
                  }
                }
              } catch (error) {
                setLoading(false);
                setProcessing(false);
                throw new Error("Error streaming")
              }
            },
            onerror: (error) => {
              setLoading(false);
              retryCounter.current++;

              if (discussionIdRef.current) {
                addMessage({ text: `An error occurred while executing step ${step.name}.`, sender: 'AI' });
              }

              setProcessing(false);
              updatedSteps[index].status = "Failed";
              setSteps(updatedSteps);
              if (retryCounter.current == 3) {
                throw error;
              }
              else {
                return null;
              }
            },
            onclose: () => {
              setLoading(false);
              setProcessing(false);
              if (abortControllerRef.current) {
                abortControllerRef.current.abort();
              }
              updatedSteps[index].status = "Completed";
              setSteps(updatedSteps);
              return Promise.resolve();
            }
          });
        } else {

          try {
            let response = await repeatStep(nodeType, nodeId, discussionId, step.name, usercomment, merge_index);
            updatedSteps[index].status = "Completed";
            setSteps(updatedSteps);
            if (response && discussionIdRef.current) {
              addMessage({ text: `Step ${step.name} executed successfully`, sender: 'AI' });
              updatedSteps[index].status = "Completed";
              setSteps(updatedSteps);
              if (step.name == "finalize" || step.name == "merge_captured_items") {
                const isCreatingProject = new URLSearchParams(window.location.search).get('is_creating_project');
                const isCreatingRequirement = new URLSearchParams(window.location.search).get('is_creating_Requirement');
                const isCreatingEpic = new URLSearchParams(window.location.search).get('is_creating_Epic');
                const node_id = new URLSearchParams(window.location.search).get('node_id');
                const isCreatingArchRequirement = new URLSearchParams(window.location.search).get('is_creating_Arch_req');
                const isCreatingSystemContext = new URLSearchParams(window.location.search).get('is_creating_system_context');
                const isCreatingInterfaces = new URLSearchParams(window.location.search).get('is_creating_Interface');
                const isCreatingComponent = new URLSearchParams(window.location.search).get('is_creating_Component');
                const isCreatingContainer = new URLSearchParams(window.location.search).get('is_creating_Container');
                const isCreatingDesign = new URLSearchParams(window.location.search).get('is_creating_Design');
                const isCreatingUserStory = new URLSearchParams(window.location.search).get('is_creating_UserStory');
                const isCreatingSystemArchitecture = new URLSearchParams(window.location.search).get('is_creating_architecture');


                resetDiscussionState()


                if (isCreatingProject === "true") {
                  const projectIds = JSON.parse(localStorage.getItem("createdProjectIds") || "[]");
                  const projectId = projectIds[projectIds.length - 1];
                  sessionStorage.setItem(`openProjectContent-${projectId}`, "true");
                  router.replace(pathName)


                } else if (isCreatingRequirement === "true") {
                  const projectIds = JSON.parse(localStorage.getItem("createdProjectIds") || "[]");
                  const projectId = projectIds[projectIds.length - 1];
                  sessionStorage.setItem(`openRequirementContent-${projectId}`, "true");
                  router.replace(pathName)

                } else if (isCreatingEpic === "true") {
                  const projectIds = JSON.parse(localStorage.getItem("createdProjectIds") || "[]");
                  const projectId = projectIds[projectIds.length - 1];
                  sessionStorage.setItem(`openEpicContent-${projectId}-${node_id}`, "true");
                  router.replace(pathName)


                } else if (isCreatingArchRequirement === "true") {
                  const projectIds = JSON.parse(localStorage.getItem("createdProjectIds") || "[]");
                  const projectId = projectIds[projectIds.length - 1];
                  sessionStorage.setItem(`openArchReqContent-${projectId}`, "true");
                  router.replace(pathName)


                }
                else if (isCreatingSystemContext === "true") {
                  const projectIds = JSON.parse(localStorage.getItem("createdProjectIds") || "[]");
                  const projectId = projectIds[projectIds.length - 1];
                  sessionStorage.setItem(`openSystemContextContent-${projectId}`, "true");
                  router.replace(pathName)


                }


                else if (isCreatingContainer === "true") {
                  const projectIds = JSON.parse(localStorage.getItem("createdProjectIds") || "[]");
                  const projectId = projectIds[projectIds.length - 1];
                  sessionStorage.setItem(`openContainerContent-${projectId}`, "true");
                  router.replace(pathName)


                }

                else if (isCreatingComponent === "true") {
                  const projectIds = JSON.parse(localStorage.getItem("createdProjectIds") || "[]");
                  const projectId = projectIds[projectIds.length - 1];
                  sessionStorage.setItem('discussion_status', "completed")
                  sessionStorage.setItem('discussion_type', "component")
                  sessionStorage.setItem(`openComponentContent-${projectId}`, "true");
                  router.replace(pathName)


                }

                else if (isCreatingSystemArchitecture === "true") {
                  const projectIds = JSON.parse(localStorage.getItem("createdProjectIds") || "[]");
                  const projectId = projectIds[projectIds.length - 1];
                  sessionStorage.setItem('discussion_status', "completed")
                  sessionStorage.setItem('discussion_type', "architecture")
                  sessionStorage.setItem(`openComponentContent-${projectId}`, "true");
                  router.replace(pathName)


                }

                else if (isCreatingInterfaces === "true") {
                  const projectIds = JSON.parse(localStorage.getItem("createdProjectIds") || "[]");
                  const projectId = projectIds[projectIds.length - 1];
                  sessionStorage.setItem(`openInterfacesContent-${projectId}`, "true");
                  router.replace(pathName)


                }

                else if (isCreatingDesign === "true") {
                  const projectIds = JSON.parse(localStorage.getItem("createdProjectIds") || "[]");
                  const projectId = projectIds[projectIds.length - 1];
                  sessionStorage.setItem(`openDesignContent-${projectId}`, "true");
                  router.replace(pathName)


                }
                else if (isCreatingUserStory === "true") {
                  const projectIds = JSON.parse(localStorage.getItem("createdProjectIds") || "[]");
                  const projectId = projectIds[projectIds.length - 1];
                  sessionStorage.setItem(`openUserStoryContent-${projectId}`, "true");
                  router.replace(pathName)


                }

                else {
                  let url = new URL(window.location.href);
                  url = url.href.split('?')[0];
                  window.location.href = url;
                  onClose();
                }

              }
            } else {
              addMessage({ text: errorMessages[step.name] || `Something went wrong. Please try again.`, sender: 'AI' });
              updatedSteps[index].status = "Failed";
              setSteps(updatedSteps);
            }
          }
          catch (error) {
            addMessage({ text: errorMessages[step.name] || `Something went wrong. Please try again.`, sender: 'AI' });
            updatedSteps[index].status = "Completed";
            setSteps(updatedSteps);
          }
          finally {
            setProcessing(false);
          }
        }
      }
      else {
        return;
      }
    }
  };

  const resetDiscussionState = () => {
    if (wsConnection) {
      wsConnection.close();
    }
    discussionClosed.current = true;
    setWsConnection(null);
    setLoading(false);
    setProcessing(false);
    setInitialized(false);

    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    abortControllerRef.current = null;
    discussionStateRef.current = "closing";

    // Reset state
    setNodeId(null);
    setNodeType(null);
    setSteps([]);
    setModifications([]);
    setAllDocuments([]);
    setWelcomeMessageShown(false);
    setMessages([]);
    setDiscussionId(null);
    setAvailableModels([]);
    setSelectedModel('');
    setIsLoadingModels(false);
    discussionClosed.current = false;
    discussionIdRef.current = null;
    discussionStateRef.current = "idle";
  };

  const isOpen = () => {
    return isDiscussionModalOpen;
  }
  const onFullScreen = () => {
    setIsFullScreen(!isFullScreen);
  }

  const stopStreaming = () => {
    if (searchParams.get('sessionId')) { //stopping code_query
      
      if (wsConnection && wsStatus === "connected") {
        wsConnection.send(JSON.stringify({
          type: "stop_streaming",
          task_id: searchParams.get('sessionId'),
          session_id: searchParams.get('sessionId'),
          auth_token: Cookies.get('idToken')
        }))
        setProcessing(false);
        setLoading(false);
      }
    }
    else {
      if (abortControllerRef.current) { //stopping normal discussion
        abortControllerRef.current.abort();
        setProcessing(false);
      }
    }
  }

  const onClose = () => {
    if (wsConnection) {
      wsConnection.close();
    }
    discussionClosed.current = true;
    discussionIdRef.current = null;
    setWsConnection(null);
    setLoading(false);
    setProcessing(false);
    setInitialized(false);

    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    abortControllerRef.current = null;
    discussionStateRef.current = "closing";

    // Reset state
    setNodeId(null);
    setNodeType(null);
    setSteps([]);
    setModifications([]);
    setAllDocuments([]);
    setWelcomeMessageShown(false);
    setMessages([{ id: 1, text: executionMessages["initialize"], sender: 'AI', messageUuid: uuid() }]);
    setDiscussionId(null);
    setAvailableModels([]);
    setSelectedModel('');
    setIsLoadingModels(false);
    discussionClosed.current = false;
    discussionStateRef.current = "idle";
  };

  const convertToUserLocalTime = (utcTimestamp) => {
    // Parse the UTC timestamp into a Date object
    const utcDate = new Date(utcTimestamp);

    // Get the offset in minutes for the user's local timezone
    const timezoneOffsetInMs = utcDate.getTimezoneOffset() * 60 * 1000;

    // Adjust the UTC date to the local timezone
    const localDate = new Date(utcDate.getTime() - timezoneOffsetInMs);

    // Return the local time in ISO string format
    return localDate.toISOString();
  };


  const addMessageForWebSocket = (message) => {
    setMessages((prevMessages) => [
      ...prevMessages,
      {
        ...message,
        id: Date.now(),
        messageUuid: message.messageUuid || uuid(),
        timestamp: message.timestamp ? convertToUserLocalTime(message.timestamp) : new Date().toISOString()
      }
    ]);
  };

  const addMessage = (message) => {
    if (searchParams.get('sessionId')) {
      addMessageForWebSocket(message);
    } else {
      setMessages((prevMessages) => [...prevMessages, message]);
    }
  };


  const removeLastMessage = (messageUuid) => {
    if (messageUuid) {
      setMessages((prevMessages) =>
        prevMessages.filter(msg => msg.messageUuid !== messageUuid)
      );
    } else {
      setMessages((prevMessages) => prevMessages.slice(0, -1));
    }
  }

  // Add effect to log changes to allDocuments
  useEffect(() => {

    // Verify each document has required properties
    allDocuments.forEach((doc, index) => {

    });
  }, [allDocuments]);

  return (
    <DiscussionChatContext.Provider value={{
      nodeId,
      nodeType,
      initialized,
      setInitialized,
      currentMessage,
      setCurrentMessage,
      loading,
      setLoading,
      messages,
      handleStepExecution,
      setMessages,
      addMessage,
      removeLastMessage,
      discussionId,
      setDiscussionId,
      steps,
      setSteps,
      modifications,
      setModifications,
      onFullScreen,
      isOpen,
      onClose,
      isFullScreen,
      setIsDiscussionModalOpen,
      executeStatus,
      setExecuteStatus,
      processing,
      setProcessing,
      setAllUsers,
      allUsers,
      wsConnection,
      wsStatus,
      allDocuments,
      setAllDocuments,
      connectWebSocket,
      currentMessageRef,
      availableModels,
      selectedModel,
      setSelectedModel,
      isLoadingModels,
      setInstantCloseStatus,
      setOpenProjectContent,
      openProjectContent,
      stopStreaming
    }}>
      {children}
    </DiscussionChatContext.Provider>
  );
}
export { DiscussionChatProvider, DiscussionChatContext };