"use client";

import React, { useContext, useEffect, useState } from 'react';
import { AlertContext } from '@/components/NotificationAlertService/AlertList';
import { fetchEventSource } from '@microsoft/fetch-event-source';
import { useRouter } from 'next/navigation';
import Cookies from 'js-cookie';
import useLocalStorage from "@/hooks/useLocalStorage";
import BuildContent, { frameworks } from '@/components/BuildContent';
import { buildOptions } from '@/components/build/BuildOptionsButtons';
import <PERSON><PERSON><PERSON>ongLogo from '@/components/KaviaLongLogo';
import LoginSignupModal from '@/components/Modal/LoginSignupModal';
import { getHeadersRaw } from '@/utils/api';
import { TopBarContext } from '@/components/Context/TopBarContext';
import { buildProjectUrl } from '@/utils/navigationHelpers';

import {
  createNewNode,
  extractTextFromFile
} from "../../utils/api";

const BuildPage = () => {
  const { showAlert } = useContext(AlertContext);
  const router = useRouter();


  const { addTab, updateTabTitle } = useContext(TopBarContext);

  const [prompt, setPrompt] = useLocalStorage("prompt", "");
  const [selectedType, setSelectedType] = useState(0);
  const [activeFramework, setActiveFramework] = useState();
  const [selectedBuildOption, setBuildOption] = useState(0);
  const [isStreaming, setIsStreaming] = useState(false);
  const [inputText, setInputText] = useState('');
  const [loadingText, setLoadingText] = useState("Building...");
  const [controller, setController] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [projectType, setProjectType] = useState('');
  const [loggedInState, setLoggedInState] = useState(false);
  const [isComplexProjectSubmitting, setComplexProjSubmitting] = useState(false);

  const naviagteToNewComplexProject = (response) => {
    const { buildProjectUrl } = require('@/utils/navigationHelpers');
    const newProjectUrl = buildProjectUrl(response.id, 'overview');
    const projectName = response.properties.Title || response.properties.Name;
    updateTabTitle(response.id, projectName);
    addTab(projectName, newProjectUrl);
    router.push(newProjectUrl);
  }

  let isCompleted = false;
  let projectId = '0';
  let archId = '0';
  let taskId = '0';

  useEffect(() => {
    if (prompt) {
      try {
        const promptObj = JSON.parse(prompt);
        setInputText(promptObj['requirement']);
        setActiveFramework(frameworks.findIndex(v => v.key === promptObj['framework']));

        // Set build option if requirement matches any build option label
        const matchedOptionIndex = buildOptions.findIndex(option =>
          option.label.toLowerCase() === promptObj['requirement'].toLowerCase()
        );
        if (matchedOptionIndex !== -1) {
          setBuildOption(matchedOptionIndex);
        }
      } catch (error) {

        setInputText('');
        setActiveFramework(-1); // Default to React JS
      }
    }
  }, []);

  useEffect(() => {
    setLoggedInState(isLoggedIn());
  }, []);

  const handleBuildOptionSelect = (index) => {
    setBuildOption(index);
  }

  const completeProjectCreate = () => {
    if (archId && projectId && taskId && !isCompleted) {
      isCompleted = true;
      const finalURL = `${buildProjectUrl(projectId, 'code')}?task_id=${taskId}`;
      router.push(finalURL);
    }
  }

  const isLoggedIn = () => {
    const idToken = Cookies.get('idToken');
    return idToken
  }

  const determinePlatform = (blueprintData, frameworks, activeFramework) => {
    console.log("=== DETERMINING PLATFORM ===");

    // First check if we have blueprint data with tech stack
    if (blueprintData?.techStack) {
      const frontendFramework = blueprintData.techStack.frontend?.[0];
      const backendFramework = blueprintData.techStack.backend?.[0];

      console.log("Frontend framework:", frontendFramework);
      console.log("Backend framework:", backendFramework);

      // If backend-only (frontend is "None" and backend is selected)
      if (frontendFramework === "None" && backendFramework !== "None" && backendFramework) {
        console.log("Backend-only project detected, using 'backend' platform");
        return "backend";
      }

      // If frontend framework is selected, get its platform type
      if (frontendFramework && frontendFramework !== "None") {
        const frontendFrameworkObj = frameworks.find(f => f.label === frontendFramework);
        if (frontendFrameworkObj) {
          const platform = Array.isArray(frontendFrameworkObj.type)
            ? frontendFrameworkObj.type[0]
            : frontendFrameworkObj.type;
          console.log("Using frontend framework platform:", platform);
          return platform;
        }
      }

      // If backend framework is selected and no frontend, use backend platform
      if (backendFramework && backendFramework !== "None") {
        const backendFrameworkObj = frameworks.find(f => f.label === backendFramework);
        if (backendFrameworkObj) {
          const platform = Array.isArray(backendFrameworkObj.type)
            ? backendFrameworkObj.type[0]
            : backendFrameworkObj.type;
          console.log("Using backend framework platform:", platform);
          return platform || "backend";
        }
      }
    }

    // Fallback to activeFramework selection from UI
    if (activeFramework !== undefined && frameworks[activeFramework]) {
      const platform = Array.isArray(frameworks[activeFramework].type)
        ? frameworks[activeFramework].type[0]
        : frameworks[activeFramework].type;
      console.log("Using active framework platform:", platform);
      return platform;
    }

    // Final fallback
    console.log("Using default platform: web");
    return "web";
  };

  const createProject = async (blueprintData = null) => {
    if (!isLoggedIn()) {
      setIsModalOpen(true);
      return null;
    }
    try {
      // Only require activeFramework if no blueprintData is provided
      if (activeFramework === undefined && !blueprintData) {
        showAlert("Please select a framework first", "error");
        return null;
      }

      setIsStreaming(true);

      const abortController = new AbortController();
      setController(abortController);
      let url = process.env.NEXT_PUBLIC_API_URL + "/node/start-project";
      const selectedSupabaseProjectId = sessionStorage.getItem('selected_supabase_project_id');
      const selectedGitHubScmId = sessionStorage.getItem('selected_github_id')
      let retryCount = 3;
      const maxRetries = 3;

      // Create a promise that will resolve with the project data
      return new Promise(async (resolve, reject) => {
        const attemptConnection = async () => {
          try {
            console.log(`Attempting connection (attempt ${retryCount + 1}/${maxRetries})`);

            // Extract data from blueprint if available
            let frameworkKey = "";
            let frameworkName = "";
            console.log("Initial frameworkKey:", frameworkKey);

            // Check if we have a valid frontend or backend framework (not "None")
            if (blueprintData?.techStack?.frontend?.[0] && blueprintData.techStack.frontend[0] !== "None") {
              // Frontend framework is selected
              frameworkName = blueprintData.techStack.frontend[0];
              frameworkKey = frameworks.find(f => f.label === frameworkName)?.key || "react";
            } else if (blueprintData?.techStack?.backend?.[0] && blueprintData.techStack.backend[0] !== "None") {
              // Backend framework is selected
              frameworkName = blueprintData.techStack.backend[0];
              frameworkKey = frameworks.find(f => f.label === frameworkName)?.key ||
                frameworkName.toLowerCase().split(' ')[0]; // Default to first word lowercase
            } else {
              // Fallback to selected framework from UI
              frameworkName = activeFramework !== undefined ? frameworks[activeFramework].label : "React JS";
              frameworkKey = activeFramework !== undefined ? frameworks[activeFramework].key : "react";
            }

            console.log("Frontend framework:", blueprintData?.techStack?.frontend?.[0]);
            console.log("Backend framework:", blueprintData?.techStack?.backend?.[0]);
            console.log("Final frameworkKey:", frameworkKey);
            console.log("Final frameworkName:", frameworkName);

            // Use the new platform determination logic
            const platform = determinePlatform(blueprintData, frameworks, activeFramework);
            console.log("Determined platform:", platform);

            // Fix blueprint data types - ensure all IDs are strings and handle nested objects
            let processedBlueprint = null;
            if (blueprintData) {
              processedBlueprint = {
                ...blueprintData,
                id: String(blueprintData.id), // Convert ID to string
              };

              // Ensure database field is present in techStack
              if (processedBlueprint.techStack) {
                // Add database field if it doesn't exist
                if (!processedBlueprint.techStack.database) {
                  processedBlueprint.techStack.database = ["None"];
                }

                // Ensure all techStack arrays exist
                if (!processedBlueprint.techStack.frontend) {
                  processedBlueprint.techStack.frontend = ["None"];
                }
                if (!processedBlueprint.techStack.backend) {
                  processedBlueprint.techStack.backend = ["None"];
                }
              }

              // Convert any nested IDs to strings
              if (processedBlueprint.features && Array.isArray(processedBlueprint.features)) {
                processedBlueprint.features = processedBlueprint.features.map(feature => ({
                  ...feature,
                  id: String(feature.id) // Convert feature IDs to strings
                }));
              }

              // Convert projectInfo ID if it exists
              if (processedBlueprint.projectInfo && processedBlueprint.projectInfo.id) {
                processedBlueprint.projectInfo = {
                  ...processedBlueprint.projectInfo,
                  id: String(processedBlueprint.projectInfo.id)
                };
              }

              console.log("Processed blueprint:", processedBlueprint);
            }

            // Prepare request body
            const requestBody = {
              "requirement": blueprintData?.description || inputText,
              "framework": frameworkKey,
              "platform": platform, // Use the dynamically determined platform
              "Supabase_project_id": selectedSupabaseProjectId,
              "encrypted_scm_id": sessionStorage.getItem('selected_github_id')
            };

            // If we have blueprint data, include the COMPLETE blueprint
            if (processedBlueprint) {
              requestBody.blueprint = processedBlueprint;
            }

            console.log("=== FINAL REQUEST BODY ===");
            console.log("Request Body:", requestBody);

            await fetchEventSource(url, {
              method: 'POST',
              body: JSON.stringify(requestBody),
              headers: {
                ...getHeadersRaw(),
                'Content-Type': 'application/json',
              },
              signal: abortController.signal,
              openWhenHidden: true,
              onopen: (response) => {
                if (response.status === 402) {
                  abortController.abort();
                  reject(new Error("Payment required"));
                }
                if (response.ok) {
                  return Promise.resolve();
                }
                reject(new Error(`Server returned ${response.status}`));
              },

              onmessage: (event) => {
                try {
                  const data = JSON.parse(event.data);
                  if (data.error) {
                    showAlert(data.error || "An error occured while building the project. Please retry in some time.", "error");
                    setTimeout(() => {
                      setIsStreaming(false);
                    }, 3000);
                    reject(new Error(data.error || "An error occurred"));
                    return;
                  }
                  if (data.container_id) archId = data.container_id;
                  if (data.task_id) taskId = data.task_id;
                  if (data.project_id) projectId = data.project_id;
                  if (data.message) setLoadingText(data.message);
                  if (data.status && data.status === "complete") {
                    if (taskId && (taskId !== '0' && taskId !== 0)) {
                      completeProjectCreate();
                    }

                    // Generate appropriate project title based on tech stack
                    let projectTitle = blueprintData?.name || inputText || "New Project";
                    if (blueprintData?.techStack) {
                      const frontendName = blueprintData.techStack.frontend?.[0];
                      const backendName = blueprintData.techStack.backend?.[0];

                      if (frontendName && frontendName !== "None" && backendName && backendName !== "None") {
                        projectTitle += ` (${frontendName} + ${backendName})`;
                      } else if (frontendName && frontendName !== "None") {
                        projectTitle += ` (${frontendName})`;
                      } else if (backendName && backendName !== "None") {
                        projectTitle += ` (${backendName})`;
                      }
                    }

                    // Resolve with the project data and include any blueprint information
                    const result = {
                      id: projectId,
                      properties: {
                        Title: projectTitle,
                        Name: projectTitle,
                        Description: blueprintData?.description || "",
                        Framework: frameworkName,
                        Platform: platform
                      }
                    };

                    const projectInfo = {
                      selected_project_id: projectId,
                      selected_project_creator_email: '',
                      is_public_selected: 'false',
                      selected_tenant_id: Cookies.get('tenant_id') || ''
                    };

                    // Store in cookies for backward compatibility
                    Cookies.set('selected_project_id', projectInfo.selected_project_id);
                    Cookies.set('is_public_selected', projectInfo.is_public_selected);
                    Cookies.set('selected_tenant_id', projectInfo.selected_tenant_id)

                    // Only include architecture pattern if it exists in the blueprint
                    if (blueprintData?.architecturePattern) {
                      result.properties.ArchitecturePattern = blueprintData.architecturePattern;
                    }

                    console.log("Resolving with result:", result);
                    resolve(result);
                  }
                } catch (error) {
                  console.error("Error processing response:", error);
                  showAlert("Error processing response", "error");
                  reject(error);
                }
              },

              onerror: (error) => {
                if (retryCount < maxRetries) {
                  retryCount++;
                  return true;
                }
                abortController.abort();
                setIsStreaming(false);
                reject(error);
                return null;
              },

              onclose: () => {
                if (!isCompleted) {
                  if (retryCount < maxRetries) {
                    retryCount++;
                    attemptConnection();
                  } else {
                    setIsStreaming(true);
                    setController(null);
                    reject(new Error("Connection closed"));
                  }
                }
              }
            });
          } catch (error) {
            if (retryCount < maxRetries) {
              retryCount++;
              await attemptConnection();
            } else {
              reject(error);
            }
          }
        };

        await attemptConnection();
      });
    } catch (error) {
      setIsStreaming(false);
      showAlert("Failed to create project: " + error.message, "error");
      return null;
    }
  };

  // New function to call the start-project endpoint
  const callStartProjectEndpoint = async (blueprintData) => {
    try {
      console.log("callStartProjectEndpoint - Input blueprintData:", blueprintData);

      // Extract framework information
      let frameworkKey = "";
      let frameworkName = "";

      if (blueprintData?.techStack?.frontend?.[0] && blueprintData.techStack.frontend[0] !== "None") {
        frameworkName = blueprintData.techStack.frontend[0];
        frameworkKey = frameworks.find(f => f.label === frameworkName)?.key || "react";
        console.log("Using frontend framework:", frameworkName);
      } else if (blueprintData?.techStack?.backend?.[0] && blueprintData.techStack.backend[0] !== "None") {
        frameworkName = blueprintData.techStack.backend[0];
        frameworkKey = frameworks.find(f => f.label === frameworkName)?.key ||
          frameworkName.toLowerCase().split(' ')[0];
        console.log("Using backend framework:", frameworkName);
      } else {
        frameworkName = activeFramework !== undefined ? frameworks[activeFramework].label : "React JS";
        frameworkKey = activeFramework !== undefined ? frameworks[activeFramework].key : "react";
        console.log("Using fallback framework:", frameworkName);
      }

      console.log("Final frameworkKey:", frameworkKey);
      console.log("Final frameworkName:", frameworkName);

      // Use the same platform determination logic
      const platform = determinePlatform(blueprintData, frameworks, activeFramework);
      console.log("Final platform:", platform);

      // Fix blueprint data types - ensure all IDs are strings and handle nested objects
      let processedBlueprint = null;
      if (blueprintData) {
        processedBlueprint = {
          ...blueprintData,
          id: String(blueprintData.id), // Convert ID to string
        };

        // Ensure database field is present in techStack
        if (processedBlueprint.techStack) {
          // Add database field if it doesn't exist
          if (!processedBlueprint.techStack.database) {
            processedBlueprint.techStack.database = ["None"];
          }

          // Ensure all techStack arrays exist
          if (!processedBlueprint.techStack.frontend) {
            processedBlueprint.techStack.frontend = ["None"];
          }
          if (!processedBlueprint.techStack.backend) {
            processedBlueprint.techStack.backend = ["None"];
          }
        }

        // Convert any nested IDs to strings
        if (processedBlueprint.features && Array.isArray(processedBlueprint.features)) {
          processedBlueprint.features = processedBlueprint.features.map(feature => ({
            ...feature,
            id: String(feature.id) // Convert feature IDs to strings
          }));
        }

        // Convert projectInfo ID if it exists
        if (processedBlueprint.projectInfo && processedBlueprint.projectInfo.id) {
          processedBlueprint.projectInfo = {
            ...processedBlueprint.projectInfo,
            id: String(processedBlueprint.projectInfo.id)
          };
        }

        console.log("Processed blueprint for callStartProjectEndpoint:", processedBlueprint);
      }

      const requestBody = {
        "requirement": blueprintData?.description || inputText || "Default project requirement",
        "framework": frameworkKey,
        "platform": platform, // Use the dynamically determined platform
        "advanced_applications": true,
        "blueprint": processedBlueprint,
        "encrypted_scm_id": sessionStorage.getItem('selected_github_id')
      };

      console.log("callStartProjectEndpoint - Request Body:", requestBody);


      const url = process.env.NEXT_PUBLIC_API_URL + "/node/start-project";
      console.log("callStartProjectEndpoint - API URL:", url);

      const response = await fetch(url, {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          ...getHeadersRaw(),
          'Content-Type': 'application/json',
        }
      });

      console.log("callStartProjectEndpoint - Response status:", response.status);

      if (!response.ok) {
        // Try to get error details
        let errorDetails = `HTTP error! status: ${response.status}`;
        try {
          const errorText = await response.text();
          console.log("callStartProjectEndpoint - Error response:", errorText);

          // Try to parse as JSON for more details
          try {
            const errorData = JSON.parse(errorText);
            console.log("callStartProjectEndpoint - Parsed error data:", errorData);
            if (errorData.detail) {
              errorDetails = `HTTP ${response.status}: ${JSON.stringify(errorData.detail)}`;
            }
          } catch (parseError) {
            errorDetails = `HTTP ${response.status}: ${errorText}`;
          }
        } catch (textError) {
          console.log("Could not read error response text");
        }

        throw new Error(errorDetails);
      }

      // Try to parse response if there's content
      let responseData = null;
      try {
        const responseText = await response.text();
        if (responseText) {
          responseData = JSON.parse(responseText);
          console.log("callStartProjectEndpoint - Success response:", responseData);
        }
      } catch (parseError) {
        console.log("callStartProjectEndpoint - No JSON response or empty response (this might be expected)");
      }

      console.log("callStartProjectEndpoint - Successfully completed");
      return responseData;

    } catch (error) {
      console.error("callStartProjectEndpoint - Error:", error);
      showAlert(`Failed to call start-project endpoint: ${error.message}`, "error");
      throw error;
    }
  };

  const handleNavigation = (path) => {
    setPrompt(JSON.stringify({
      "requirement": inputText,
      "framework": activeFramework !== undefined ? frameworks[activeFramework].key : ""
    }));
    router.push(path);
  }

  const uploadAssets = async (projectResponse, files) => {
    try {
      if (!files || files.length === 0) {
        showAlert("No files to upload. Redirecting to project page...", "info");
        setTimeout(() => {
          naviagteToNewComplexProject(projectResponse);
        }, 1000);
        return;
      }

      let successCount = 0;
      let errorCount = 0;
      const uploadResults = [];

      // Process each file sequentially
      for (let i = 0; i < files.length; i++) {
        const file = files[i];

        if (file) {
          try {
            const result = await extractTextFromFile(projectResponse.id, file);

            if (result) {
              successCount++;
              uploadResults.push({
                file: file.name,
                status: 'success',
                file_uuid: result.files?.[0]?.file_uuid
              });
            } else {
              errorCount++;
              uploadResults.push({
                file: file.name,
                status: 'error',
                error: 'No result returned from server'
              });
              showAlert(`Failed to upload ${file.name}: No result returned`, "error");
            }
          } catch (error) {
            errorCount++;
            const errorMessage = error instanceof Error ? error.message : "Unknown error";
            uploadResults.push({
              file: file.name,
              status: 'error',
              error: errorMessage
            });
            console.error(`Upload error for ${file.name}:`, error);
            showAlert(`Failed to upload ${file.name}: ${errorMessage}`, "error");
          }
        }
      }

      // Show final summary
      if (successCount > 0 && errorCount === 0) {
        showAlert(`All ${successCount} files uploaded successfully. Redirecting to project page...`, "success");
      } else if (successCount > 0 && errorCount > 0) {
        showAlert(`${successCount} files uploaded successfully, ${errorCount} failed. Redirecting to project page...`, "info");
      } else if (errorCount > 0) {
        showAlert(`All ${errorCount} files failed to upload. Redirecting to project page...`, "error");
      }

      console.log('Upload results:', uploadResults);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "An unknown error occurred";
      showAlert(`Failed to upload assets: ${errorMessage}`, "error");
    } finally {
      setTimeout(() => {
        naviagteToNewComplexProject(projectResponse);
      }, 2000);
    }
  };

  const handleComplexProjectSubmit = async (formData) => {
    if (!isLoggedIn()) {
      setIsModalOpen(true);
      return;
    }
    console.error("formData", formData)


    if (!formData.name.trim() && !formData.description.trim()) {
      showAlert("Project name and description are required.", "danger");
      return;
    }

    setComplexProjSubmitting(true);
    try {
      let response;
      const isAdvancedApplication = formData.projectInfo?.id || formData.blueprint?.projectInfo?.id;

      if (isAdvancedApplication) {
        // Use existing project from advanced application
        const projectId = formData.projectInfo?.id || formData.blueprint?.projectInfo?.id;

        response = {
          id: projectId,
          properties: {
            Title: formData.name?.trim() || formData.blueprint?.name || "Advanced Application",
            Description: formData.description?.trim() || formData.blueprint?.description || ""
          }
        };
      } else {
        response = await createNewNode(
          "Project",
          formData.name.trim(),
          formData.description.trim(),
          {}
        );
      }


      if (response != null) {
        const projectId = response.id.toString();

        const projectInfo = {
          selected_project_id: projectId,
          selected_project_creator_email: '',
          is_public_selected: 'false',
          selected_tenant_id: Cookies.get('tenant_id') || ''
        };

        // Store in cookies for backward compatibility
        Cookies.set('selected_project_id', projectInfo.selected_project_id);
        Cookies.set('is_public_selected', projectInfo.is_public_selected);
        Cookies.set('selected_tenant_id', projectInfo.selected_tenant_id);

        // // Call the start-project endpoint here with blueprint data
        try {
          console.log("=== CALLING START-PROJECT ENDPOINT AFTER COOKIE SETUP ===");
          const startProjectResponse = await callStartProjectEndpoint(formData.blueprint || formData);
          console.log("Start-project endpoint response:", startProjectResponse);
        } catch (endpointError) {
          console.error("Error calling start-project endpoint:", endpointError);
        }
        if (formData.files && formData.files.length > 0) {
          showAlert("Project built successfully. Starting file uploads...", "success");
          // Wait for uploadAssets to complete before proceeding
          await uploadAssets(response, formData.files);
        } else {
          showAlert(
            "Project built successfully. No assets to upload.",
            "success"
          );
          if (response && response.id) {
            const { navigateToProject } = await import('@/utils/navigationHelpers');
            navigateToProject(router, response.id, 'overview');
          }
        }
      } else {
        setComplexProjSubmitting(false);
        showAlert("An unexpected error occurred", "danger");
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "An unknown error occurred";
      showAlert(`Failed to create the Project: ${errorMessage}`, "danger");
      setComplexProjSubmitting(false);
    } finally {
      // Reset submitting state after everything is done
      setComplexProjSubmitting(false);
    }
  };



  return (
    <div className={`relative h-screen px-4 bg-[#231f20] flex flex-col justify-center transition-colors duration-500 ease-in-out ${!loggedInState ? 'overflow-hidden' : 'overflow-hidden -mr-4'
      }`}>


      {/* Gradient circle in top right - adjust opacity and colors based on theme */}
      <div className="absolute -top-80 -right-40 w-1/2 h-96 rounded-full bg-gradient-to-br from-primary-600/60 to-primary-800/50 blur-3xl opacity-50 transition-all duration-700 ease-in-out"></div>
      {!loggedInState &&
        <div className={`fixed left-0 right-0 top-0 flex justify-between items-center m-6 transition-opacity duration-500 ease-in-out ${loggedInState ? 'opacity-0 pointer-events-none' : 'opacity-100'}`}>           <KaviaLongLogo />
          <div className='flex justify-center items-center'>
            <button
              onClick={() => router.push('/users/login')}
              className='text-white mr-4 transition-colors duration-300 hover:text-primary-300'
            >
              Login
            </button>
            <button
              className={`px-4 py-2 bg-primary rounded-md text-white transition-colors duration-300 hover:bg-primary-600`}
              onClick={() => router.push('/users/sign_up')}
            >
              Sign Up
            </button>
          </div>
        </div>
      }

      {/* Main Content */}
      <div className={`${!loggedInState ? 'w-full max-w-none' : 'container'} mx-auto`}>
        <BuildContent
          loggedInState={loggedInState}
          selectedType={selectedType}
          setSelectedType={setSelectedType}
          selectedBuildOption={selectedBuildOption}
          setBuildOption={handleBuildOptionSelect}
          activeFramework={activeFramework}
          setActiveFramework={setActiveFramework}
          createProject={createProject}
          handleComplexProjectSubmit={handleComplexProjectSubmit}
          isComplexProjectSubmitting={isComplexProjectSubmitting}
          setIsModalOpen={setIsModalOpen}
          isStreaming={isStreaming}
          loadingText={loadingText}
          inputText={inputText}
          setInputText={setInputText}
          showAlert={showAlert}
          router={router}
          forceDarkMode={!loggedInState}
        />
      </div>

      {isModalOpen && <LoginSignupModal handleClick={handleNavigation} setIsModalOpen={setIsModalOpen} />}
    </div>
  );
};

export default BuildPage;